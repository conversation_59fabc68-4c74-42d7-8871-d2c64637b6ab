#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')
django.setup()

from accounts.models import User
from departments.models import Department

def create_initial_data():
    print("إنشاء البيانات الأولية...")
    
    # إنشاء الأقسام
    hr_dept, created = Department.objects.get_or_create(
        code='HR001',
        defaults={
            'name': 'قسم الموارد البشرية',
            'name_en': 'Human Resources Department',
            'department_type': 'hr',
            'description': 'قسم إدارة الموارد البشرية والشؤون الإدارية'
        }
    )
    if created:
        print("تم إنشاء قسم الموارد البشرية")
    
    accounts_dept, created = Department.objects.get_or_create(
        code='ACC001',
        defaults={
            'name': 'قسم الحسابات',
            'name_en': 'Accounts Department', 
            'department_type': 'accounts',
            'description': 'قسم الحسابات والشؤون المالية'
        }
    )
    if created:
        print("تم إنشاء قسم الحسابات")
    
    registration_dept, created = Department.objects.get_or_create(
        code='REG001',
        defaults={
            'name': 'قسم التسجيل',
            'name_en': 'Registration Department',
            'department_type': 'registration', 
            'description': 'قسم التسجيل وشؤون الطلاب'
        }
    )
    if created:
        print("تم إنشاء قسم التسجيل")
    
    # إنشاء مستخدم إداري
    if not User.objects.filter(username='admin').exists():
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name='مدير',
            last_name='النظام',
            user_type='admin',
            employee_id='EMP001',
            department=hr_dept
        )
        print("تم إنشاء المستخدم الإداري بنجاح")
        print("اسم المستخدم: admin")
        print("كلمة المرور: admin123")
    else:
        print("المستخدم الإداري موجود مسبقاً")
    
    print("تم الانتهاء من إنشاء البيانات الأولية")

if __name__ == '__main__':
    create_initial_data()
