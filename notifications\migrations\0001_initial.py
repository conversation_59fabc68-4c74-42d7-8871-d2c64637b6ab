# Generated by Django 5.2.1 on 2025-05-24 14:33

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('departments', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القالب')),
                ('notification_type', models.CharField(choices=[('admin', 'إداري'), ('academic', 'أكاديمي'), ('leave', 'إجازة'), ('document', 'مستند'), ('system', 'نظام'), ('reminder', 'تذكير')], max_length=20, verbose_name='نوع التنبيه')),
                ('title_template', models.CharField(help_text='يمكن استخدام متغيرات مثل {user_name}, {date}', max_length=200, verbose_name='قالب العنوان')),
                ('message_template', models.TextField(help_text='يمكن استخدام متغيرات مثل {user_name}, {date}', verbose_name='قالب الرسالة')),
                ('default_priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=10, verbose_name='الأولوية الافتراضية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'قالب تنبيه',
                'verbose_name_plural': 'قوالب التنبيهات',
            },
        ),
        migrations.CreateModel(
            name='BulkNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان التنبيه')),
                ('message', models.TextField(verbose_name='نص التنبيه')),
                ('notification_type', models.CharField(choices=[('admin', 'إداري'), ('academic', 'أكاديمي'), ('leave', 'إجازة'), ('document', 'مستند'), ('system', 'نظام'), ('reminder', 'تذكير')], max_length=20, verbose_name='نوع التنبيه')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('target_type', models.CharField(choices=[('all', 'جميع المستخدمين'), ('department', 'قسم محدد'), ('user_type', 'نوع مستخدم محدد'), ('custom', 'مستخدمون محددون')], max_length=20, verbose_name='نوع الهدف')),
                ('target_user_type', models.CharField(blank=True, choices=[], max_length=20, null=True, verbose_name='نوع المستخدم المستهدف')),
                ('is_sent', models.BooleanField(default=False, verbose_name='تم الإرسال')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت الإرسال')),
                ('recipients_count', models.PositiveIntegerField(default=0, verbose_name='عدد المستقبلين')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('sender', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_bulk_notifications', to=settings.AUTH_USER_MODEL, verbose_name='المرسل')),
                ('target_department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='departments.department', verbose_name='القسم المستهدف')),
                ('target_users', models.ManyToManyField(blank=True, to=settings.AUTH_USER_MODEL, verbose_name='المستخدمون المستهدفون')),
            ],
            options={
                'verbose_name': 'تنبيه جماعي',
                'verbose_name_plural': 'التنبيهات الجماعية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان التنبيه')),
                ('message', models.TextField(verbose_name='نص التنبيه')),
                ('notification_type', models.CharField(choices=[('admin', 'إداري'), ('academic', 'أكاديمي'), ('leave', 'إجازة'), ('document', 'مستند'), ('system', 'نظام'), ('reminder', 'تذكير')], max_length=20, verbose_name='نوع التنبيه')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت القراءة')),
                ('related_object_type', models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع الكائن المرتبط')),
                ('related_object_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='معرف الكائن المرتبط')),
                ('action_url', models.URLField(blank=True, null=True, verbose_name='رابط الإجراء')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='ينتهي في')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_notifications', to=settings.AUTH_USER_MODEL, verbose_name='المستقبل')),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_notifications', to=settings.AUTH_USER_MODEL, verbose_name='المرسل')),
            ],
            options={
                'verbose_name': 'تنبيه',
                'verbose_name_plural': 'التنبيهات',
                'ordering': ['-created_at'],
            },
        ),
    ]
