from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from datetime import date, timedelta

from .models import Employee, Leave, Attendance, EmployeeDocument, Promotion
from accounts.models import User
from departments.models import Department


class EmployeeForm(forms.ModelForm):
    """نموذج إضافة/تعديل الموظف"""
    
    # حقول المستخدم
    username = forms.CharField(
        label=_('اسم المستخدم'),
        max_length=150,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    
    first_name = forms.CharField(
        label=_('الاسم الأول'),
        max_length=30,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    
    last_name = forms.CharField(
        label=_('اسم العائلة'),
        max_length=30,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    
    email = forms.EmailField(
        label=_('البريد الإلكتروني'),
        widget=forms.EmailInput(attrs={'class': 'form-control'})
    )
    
    phone = forms.CharField(
        label=_('رقم الهاتف'),
        max_length=15,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    
    employee_id = forms.CharField(
        label=_('رقم الموظف'),
        max_length=20,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    
    department = forms.ModelChoiceField(
        label=_('القسم'),
        queryset=Department.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    user_type = forms.ChoiceField(
        label=_('نوع المستخدم'),
        choices=User.USER_TYPES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    class Meta:
        model = Employee
        fields = [
            'national_id', 'birth_date', 'gender', 'marital_status',
            'address', 'emergency_contact_name', 'emergency_contact_phone',
            'hire_date', 'position', 'salary'
        ]
        
        widgets = {
            'national_id': forms.TextInput(attrs={'class': 'form-control'}),
            'birth_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'gender': forms.Select(attrs={'class': 'form-select'}),
            'marital_status': forms.Select(attrs={'class': 'form-select'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'emergency_contact_name': forms.TextInput(attrs={'class': 'form-control'}),
            'emergency_contact_phone': forms.TextInput(attrs={'class': 'form-control'}),
            'hire_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'position': forms.TextInput(attrs={'class': 'form-control'}),
            'salary': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        }
    
    def __init__(self, *args, **kwargs):
        self.user_instance = kwargs.pop('user_instance', None)
        super().__init__(*args, **kwargs)
        
        if self.user_instance:
            # ملء الحقول من بيانات المستخدم الموجود
            self.fields['username'].initial = self.user_instance.username
            self.fields['first_name'].initial = self.user_instance.first_name
            self.fields['last_name'].initial = self.user_instance.last_name
            self.fields['email'].initial = self.user_instance.email
            self.fields['phone'].initial = self.user_instance.phone
            self.fields['employee_id'].initial = self.user_instance.employee_id
            self.fields['department'].initial = self.user_instance.department
            self.fields['user_type'].initial = self.user_instance.user_type
    
    def clean_employee_id(self):
        employee_id = self.cleaned_data['employee_id']
        
        # التحقق من عدم تكرار رقم الموظف
        existing_user = User.objects.filter(employee_id=employee_id)
        if self.user_instance:
            existing_user = existing_user.exclude(id=self.user_instance.id)
        
        if existing_user.exists():
            raise ValidationError(_('رقم الموظف موجود مسبقاً'))
        
        return employee_id
    
    def clean_birth_date(self):
        birth_date = self.cleaned_data['birth_date']
        
        # التحقق من أن العمر أكبر من 18 سنة
        today = date.today()
        age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
        
        if age < 18:
            raise ValidationError(_('يجب أن يكون عمر الموظف 18 سنة على الأقل'))
        
        if age > 70:
            raise ValidationError(_('عمر الموظف كبير جداً'))
        
        return birth_date


class LeaveForm(forms.ModelForm):
    """نموذج طلب الإجازة"""
    
    class Meta:
        model = Leave
        fields = ['leave_type', 'start_date', 'end_date', 'reason']
        
        widgets = {
            'leave_type': forms.Select(attrs={'class': 'form-select'}),
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'reason': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
        }
    
    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        
        if start_date and end_date:
            # التحقق من أن تاريخ النهاية بعد تاريخ البداية
            if end_date < start_date:
                raise ValidationError(_('تاريخ انتهاء الإجازة يجب أن يكون بعد تاريخ البداية'))
            
            # التحقق من أن الإجازة في المستقبل
            if start_date < date.today():
                raise ValidationError(_('لا يمكن طلب إجازة في الماضي'))
            
            # التحقق من مدة الإجازة
            duration = (end_date - start_date).days + 1
            if duration > 30:
                raise ValidationError(_('مدة الإجازة لا يمكن أن تزيد عن 30 يوماً'))
        
        return cleaned_data


class AttendanceForm(forms.ModelForm):
    """نموذج تسجيل الحضور"""
    
    class Meta:
        model = Attendance
        fields = ['date', 'check_in', 'check_out', 'is_present', 'notes']
        
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'check_in': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'check_out': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'is_present': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def clean(self):
        cleaned_data = super().clean()
        check_in = cleaned_data.get('check_in')
        check_out = cleaned_data.get('check_out')
        is_present = cleaned_data.get('is_present')
        
        if is_present and check_in and check_out:
            if check_out <= check_in:
                raise ValidationError(_('وقت الانصراف يجب أن يكون بعد وقت الحضور'))
        
        return cleaned_data


class EmployeeDocumentForm(forms.ModelForm):
    """نموذج رفع مستندات الموظف"""
    
    class Meta:
        model = EmployeeDocument
        fields = ['document_type', 'title', 'file']
        
        widgets = {
            'document_type': forms.Select(attrs={'class': 'form-select'}),
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'file': forms.FileInput(attrs={'class': 'form-control', 'accept': '.pdf'}),
        }
    
    def clean_file(self):
        file = self.cleaned_data['file']
        
        if file:
            # التحقق من نوع الملف
            if not file.name.endswith('.pdf'):
                raise ValidationError(_('يجب أن يكون الملف من نوع PDF'))
            
            # التحقق من حجم الملف (5 ميجابايت كحد أقصى)
            if file.size > 5 * 1024 * 1024:
                raise ValidationError(_('حجم الملف يجب أن يكون أقل من 5 ميجابايت'))
        
        return file
