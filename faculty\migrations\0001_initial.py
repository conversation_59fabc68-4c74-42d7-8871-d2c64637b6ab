# Generated by Django 5.2.1 on 2025-05-24 14:33

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('departments', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Course',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز المقرر')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المقرر')),
                ('name_en', models.CharField(blank=True, max_length=200, null=True, verbose_name='اسم المقرر بالإنجليزية')),
                ('credit_hours', models.IntegerField(verbose_name='الساعات المعتمدة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف المقرر')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='departments.department', verbose_name='القسم')),
                ('prerequisites', models.ManyToManyField(blank=True, to='faculty.course', verbose_name='المتطلبات السابقة')),
            ],
            options={
                'verbose_name': 'مقرر',
                'verbose_name_plural': 'المقررات',
            },
        ),
        migrations.CreateModel(
            name='FacultyMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('academic_rank', models.CharField(choices=[('assistant_lecturer', 'مدرس مساعد'), ('lecturer', 'مدرس'), ('assistant_professor', 'أستاذ مساعد'), ('associate_professor', 'أستاذ مشارك'), ('professor', 'أستاذ')], max_length=30, verbose_name='الرتبة الأكاديمية')),
                ('specialization', models.CharField(max_length=200, verbose_name='التخصص')),
                ('highest_degree', models.CharField(max_length=100, verbose_name='أعلى شهادة')),
                ('university_graduated', models.CharField(max_length=200, verbose_name='الجامعة المتخرج منها')),
                ('graduation_year', models.IntegerField(verbose_name='سنة التخرج')),
                ('employment_type', models.CharField(choices=[('full_time', 'دوام كامل'), ('part_time', 'دوام جزئي'), ('visiting', 'زائر'), ('adjunct', 'منتدب')], default='full_time', max_length=20, verbose_name='نوع التوظيف')),
                ('hire_date', models.DateField(verbose_name='تاريخ التوظيف')),
                ('office_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم المكتب')),
                ('office_hours', models.TextField(blank=True, null=True, verbose_name='ساعات المكتب')),
                ('research_interests', models.TextField(blank=True, null=True, verbose_name='الاهتمامات البحثية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'عضو هيئة تدريس',
                'verbose_name_plural': 'أعضاء هيئة التدريس',
            },
        ),
        migrations.CreateModel(
            name='FacultyDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('cv', 'السيرة الذاتية'), ('degree', 'شهادة علمية'), ('transcript', 'كشف درجات'), ('research', 'بحث علمي'), ('certificate', 'شهادة تقدير'), ('contract', 'عقد عمل'), ('other', 'أخرى')], max_length=20, verbose_name='نوع المستند')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان المستند')),
                ('file', models.FileField(upload_to='faculty_documents/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf'])], verbose_name='الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='رفع بواسطة')),
                ('faculty_member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='faculty.facultymember', verbose_name='عضو هيئة التدريس')),
            ],
            options={
                'verbose_name': 'مستند عضو هيئة تدريس',
                'verbose_name_plural': 'مستندات أعضاء هيئة التدريس',
            },
        ),
        migrations.CreateModel(
            name='Schedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('semester', models.CharField(choices=[('fall', 'الفصل الأول'), ('spring', 'الفصل الثاني'), ('summer', 'الفصل الصيفي')], max_length=10, verbose_name='الفصل الدراسي')),
                ('academic_year', models.CharField(help_text='مثال: 2023-2024', max_length=9, verbose_name='السنة الأكاديمية')),
                ('day_of_week', models.CharField(choices=[('sunday', 'الأحد'), ('monday', 'الاثنين'), ('tuesday', 'الثلاثاء'), ('wednesday', 'الأربعاء'), ('thursday', 'الخميس'), ('friday', 'الجمعة'), ('saturday', 'السبت')], max_length=10, verbose_name='يوم الأسبوع')),
                ('start_time', models.TimeField(verbose_name='وقت البداية')),
                ('end_time', models.TimeField(verbose_name='وقت النهاية')),
                ('classroom', models.CharField(max_length=50, verbose_name='القاعة الدراسية')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='faculty.course', verbose_name='المقرر')),
                ('faculty_member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='faculty.facultymember', verbose_name='عضو هيئة التدريس')),
            ],
            options={
                'verbose_name': 'جدول دراسي',
                'verbose_name_plural': 'الجداول الدراسية',
                'unique_together': {('faculty_member', 'course', 'semester', 'academic_year', 'day_of_week', 'start_time')},
            },
        ),
    ]
