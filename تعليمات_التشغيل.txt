نظام إدارة الموارد البشرية - الكلية التربوية المفتوحة
=====================================================

تعليمات التشغيل:
================

1. تشغيل النظام:
   - انقر نقراً مزدوجاً على ملف run.bat
   أو
   - افتح Command Prompt في مجلد المشروع واكتب:
     python manage.py runserver

2. الوصول للنظام:
   - افتح المتصفح واذهب إلى: http://127.0.0.1:8000

3. بيانات تسجيل الدخول:
   - اسم المستخدم: admin
   - كلمة المرور: admin123

4. لوحة الإدارة:
   - الرابط: http://127.0.0.1:8000/admin
   - نفس بيانات تسجيل الدخول

المميزات المتاحة:
=================

✓ لوحة تحكم رئيسية
✓ إدارة المستخدمين والصلاحيات
✓ إدارة الموظفين
✓ إدارة أعضاء هيئة التدريس
✓ الأقسام الإدارية (الموارد البشرية، الحسابات، التسجيل)
✓ نظام أرشفة المستندات
✓ نظام التنبيهات
✓ التقارير (PDF/Excel)

أنواع المستخدمين:
==================

1. مدير النظام (admin) - صلاحيات كاملة
2. موظف الموارد البشرية (hr) - إدارة الموظفين والتقارير
3. موظف شؤون الطلاب (student_affairs) - إدارة أعضاء هيئة التدريس
4. محاضر (lecturer) - عرض البيانات الشخصية والجداول
5. موظف (employee) - عرض البيانات الشخصية وطلب الإجازات

ملاحظات مهمة:
==============

- تأكد من تثبيت Python 3.8 أو أحدث
- تأكد من تثبيت المكتبات المطلوبة: pip install -r requirements.txt
- النظام يستخدم قاعدة بيانات SQLite محلية
- جميع الملفات المرفوعة تحفظ في مجلد media/
- النظام يدعم اللغة العربية بالكامل

للدعم التقني:
==============

في حالة وجود مشاكل:
1. تأكد من تثبيت جميع المتطلبات
2. تحقق من رسائل الخطأ في Command Prompt
3. تأكد من عدم استخدام المنفذ 8000 من برنامج آخر

تم تطوير النظام خصيصاً للكلية التربوية المفتوحة
تاريخ الإنشاء: 2024
