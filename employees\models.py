from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import FileExtensionValidator


class Employee(models.Model):
    """نموذج بيانات الموظفين"""

    GENDER_CHOICES = (
        ('male', _('ذكر')),
        ('female', _('أنثى')),
    )

    MARITAL_STATUS_CHOICES = (
        ('single', _('أعزب')),
        ('married', _('متزوج')),
        ('divorced', _('مطلق')),
        ('widowed', _('أرمل')),
    )

    user = models.OneToOneField(
        'accounts.User',
        on_delete=models.CASCADE,
        verbose_name=_('المستخدم')
    )

    # المعلومات الشخصية
    national_id = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('رقم الهوية الوطنية')
    )

    birth_date = models.DateField(
        verbose_name=_('تاريخ الميلاد')
    )

    gender = models.CharField(
        max_length=10,
        choices=GENDER_CHOICES,
        verbose_name=_('الجنس')
    )

    marital_status = models.CharField(
        max_length=10,
        choices=MARITAL_STATUS_CHOICES,
        verbose_name=_('الحالة الاجتماعية')
    )

    address = models.TextField(
        verbose_name=_('العنوان')
    )

    emergency_contact_name = models.CharField(
        max_length=100,
        verbose_name=_('اسم جهة الاتصال في حالات الطوارئ')
    )

    emergency_contact_phone = models.CharField(
        max_length=15,
        verbose_name=_('هاتف جهة الاتصال في حالات الطوارئ')
    )

    # معلومات العمل
    hire_date = models.DateField(
        verbose_name=_('تاريخ التوظيف')
    )

    position = models.CharField(
        max_length=100,
        verbose_name=_('المنصب')
    )

    salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('الراتب')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('موظف')
        verbose_name_plural = _('الموظفون')

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.position}"


class EmployeeDocument(models.Model):
    """نموذج مستمسكات الموظفين"""

    DOCUMENT_TYPES = (
        ('id_copy', _('صورة الهوية')),
        ('certificate', _('شهادة')),
        ('contract', _('عقد عمل')),
        ('medical', _('تقرير طبي')),
        ('other', _('أخرى')),
    )

    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='documents',
        verbose_name=_('الموظف')
    )

    document_type = models.CharField(
        max_length=20,
        choices=DOCUMENT_TYPES,
        verbose_name=_('نوع المستند')
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان المستند')
    )

    file = models.FileField(
        upload_to='employee_documents/',
        validators=[FileExtensionValidator(allowed_extensions=['pdf'])],
        verbose_name=_('الملف')
    )

    uploaded_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('رفع بواسطة')
    )

    uploaded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _('مستند موظف')
        verbose_name_plural = _('مستندات الموظفين')

    def __str__(self):
        return f"{self.employee.user.get_full_name()} - {self.title}"


class Leave(models.Model):
    """نموذج الإجازات"""

    LEAVE_TYPES = (
        ('annual', _('إجازة سنوية')),
        ('sick', _('إجازة مرضية')),
        ('emergency', _('إجازة طارئة')),
        ('maternity', _('إجازة أمومة')),
        ('study', _('إجازة دراسية')),
    )

    STATUS_CHOICES = (
        ('pending', _('في الانتظار')),
        ('approved', _('موافق عليها')),
        ('rejected', _('مرفوضة')),
    )

    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='leaves',
        verbose_name=_('الموظف')
    )

    leave_type = models.CharField(
        max_length=20,
        choices=LEAVE_TYPES,
        verbose_name=_('نوع الإجازة')
    )

    start_date = models.DateField(
        verbose_name=_('تاريخ البداية')
    )

    end_date = models.DateField(
        verbose_name=_('تاريخ النهاية')
    )

    reason = models.TextField(
        verbose_name=_('السبب')
    )

    status = models.CharField(
        max_length=10,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('الحالة')
    )

    approved_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_leaves',
        verbose_name=_('موافق بواسطة')
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('إجازة')
        verbose_name_plural = _('الإجازات')

    def __str__(self):
        return f"{self.employee.user.get_full_name()} - {self.get_leave_type_display()}"

    @property
    def duration_days(self):
        return (self.end_date - self.start_date).days + 1


class Attendance(models.Model):
    """نموذج الحضور والانصراف"""

    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='attendances',
        verbose_name=_('الموظف')
    )

    date = models.DateField(
        verbose_name=_('التاريخ')
    )

    check_in = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('وقت الحضور')
    )

    check_out = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('وقت الانصراف')
    )

    is_present = models.BooleanField(
        default=True,
        verbose_name=_('حاضر')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('حضور')
        verbose_name_plural = _('الحضور')
        unique_together = ['employee', 'date']

    def __str__(self):
        return f"{self.employee.user.get_full_name()} - {self.date}"

    @property
    def working_hours(self):
        if self.check_in and self.check_out:
            from datetime import datetime, timedelta
            check_in_dt = datetime.combine(self.date, self.check_in)
            check_out_dt = datetime.combine(self.date, self.check_out)
            return check_out_dt - check_in_dt
        return None


class Promotion(models.Model):
    """نموذج الترقيات"""

    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='promotions',
        verbose_name=_('الموظف')
    )

    old_position = models.CharField(
        max_length=100,
        verbose_name=_('المنصب السابق')
    )

    new_position = models.CharField(
        max_length=100,
        verbose_name=_('المنصب الجديد')
    )

    old_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('الراتب السابق')
    )

    new_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('الراتب الجديد')
    )

    promotion_date = models.DateField(
        verbose_name=_('تاريخ الترقية')
    )

    reason = models.TextField(
        verbose_name=_('سبب الترقية')
    )

    approved_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('موافق بواسطة')
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _('ترقية')
        verbose_name_plural = _('الترقيات')

    def __str__(self):
        return f"{self.employee.user.get_full_name()} - {self.new_position}"


