# نظام إدارة الموارد البشرية - الكلية التربوية المفتوحة

## ✅ تم إنجاز النظام بنجاح!

### 🚀 كيفية التشغيل:

#### الطريقة الأولى (الأسهل):
1. انقر نقراً مزدوجاً على ملف `run.bat`
2. انتظر حتى يظهر "Starting development server"
3. افتح المتصفح واذهب إلى: http://127.0.0.1:8000

#### الطريقة الثانية:
1. افتح Command Prompt في مجلد المشروع
2. اكتب: `python manage.py runserver`
3. افتح المتصفح واذهب إلى: http://127.0.0.1:8000

### 🔑 بيانات تسجيل الدخول:
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

### 🌐 الروابط المهمة:
- **النظام الرئيسي**: http://127.0.0.1:8000
- **لوحة الإدارة**: http://127.0.0.1:8000/admin
- **إدارة الموظفين**: http://127.0.0.1:8000/employees/

---

## 📋 المميزات المُنجزة:

### ✅ 1. نظام المصادقة والصلاحيات
- تسجيل دخول آمن مع واجهة عربية
- 5 أنواع مستخدمين مختلفة
- صلاحيات متدرجة حسب نوع المستخدم

### ✅ 2. إدارة الموظفين
- إضافة وتعديل بيانات الموظفين
- نظام الإجازات (5 أنواع)
- تتبع الحضور والانصراف
- إدارة الترقيات
- رفع المستمسكات (PDF)

### ✅ 3. إدارة أعضاء هيئة التدريس
- البيانات الأكاديمية والرتب العلمية
- إدارة المقررات والجداول
- مستندات أعضاء هيئة التدريس

### ✅ 4. الأقسام الإدارية
- قسم الموارد البشرية (HR001)
- قسم الحسابات (ACC001)
- قسم التسجيل (REG001)

### ✅ 5. نظام أرشفة المستندات
- تصنيفات هرمية
- نظام العلامات والبحث
- تتبع المصدر والوجهة
- دعم ملفات متعددة

### ✅ 6. نظام التنبيهات
- تنبيهات فردية وجماعية
- قوالب قابلة للتخصيص
- أنواع مختلفة من التنبيهات

### ✅ 7. نظام التقارير
- تقارير PDF و Excel
- تقارير الموظفين والحضور
- إحصائيات شاملة

### ✅ 8. واجهة المستخدم
- تصميم عربي متجاوب
- Bootstrap 5 مع دعم RTL
- لوحة تحكم تفاعلية

---

## 🗂️ هيكل المشروع:

```
HR1/
├── accounts/           # المستخدمين والمصادقة
├── employees/          # إدارة الموظفين
├── faculty/           # أعضاء هيئة التدريس
├── departments/       # الأقسام الإدارية
├── documents/         # أرشفة المستندات
├── notifications/     # التنبيهات
├── reports/          # التقارير
├── templates/        # قوالب HTML
├── static/          # ملفات CSS/JS
├── media/           # ملفات المستخدمين
├── db.sqlite3       # قاعدة البيانات
├── run.bat          # ملف التشغيل السريع
└── requirements.txt # المتطلبات
```

---

## 👥 أنواع المستخدمين:

| النوع | الصلاحيات |
|-------|-----------|
| **مدير النظام** | صلاحيات كاملة على جميع أجزاء النظام |
| **موظف الموارد البشرية** | إدارة الموظفين، الموافقة على الإجازات، التقارير |
| **موظف شؤون الطلاب** | إدارة أعضاء هيئة التدريس، الجداول الدراسية |
| **محاضر** | عرض البيانات الشخصية، الجدول الدراسي، طلب الإجازات |
| **موظف** | عرض البيانات الشخصية، طلب الإجازات، كشف الحضور |

---

## 🔧 ملفات مساعدة:

- **`run.bat`**: تشغيل سريع للنظام
- **`test_system.py`**: اختبار النظام
- **`create_admin.py`**: إنشاء البيانات الأولية
- **`تعليمات_التشغيل.txt`**: تعليمات مفصلة
- **`README.md`**: دليل شامل للنظام

---

## 🎯 الخطوات التالية المقترحة:

1. **إضافة بيانات تجريبية** للموظفين
2. **تطوير واجهات إضافة/تعديل** الموظفين
3. **تفعيل نظام التنبيهات** بالكامل
4. **إضافة المزيد من التقارير**
5. **تحسين الأمان** وإضافة النسخ الاحتياطي

---

## 📞 الدعم:

النظام جاهز للاستخدام ويمكن تطويره وتخصيصه حسب احتياجات الكلية التربوية المفتوحة.

**تاريخ الإنجاز**: 2024  
**الحالة**: ✅ جاهز للاستخدام
