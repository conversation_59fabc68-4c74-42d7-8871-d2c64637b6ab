# Generated by Django 5.2.1 on 2025-05-24 14:33

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('national_id', models.CharField(max_length=20, unique=True, verbose_name='رقم الهوية الوطنية')),
                ('birth_date', models.DateField(verbose_name='تاريخ الميلاد')),
                ('gender', models.CharField(choices=[('male', 'ذكر'), ('female', 'أنثى')], max_length=10, verbose_name='الجنس')),
                ('marital_status', models.Char<PERSON>ield(choices=[('single', 'أعزب'), ('married', 'متزوج'), ('divorced', 'مطلق'), ('widowed', 'أرمل')], max_length=10, verbose_name='الحالة الاجتماعية')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('emergency_contact_name', models.CharField(max_length=100, verbose_name='اسم جهة الاتصال في حالات الطوارئ')),
                ('emergency_contact_phone', models.CharField(max_length=15, verbose_name='هاتف جهة الاتصال في حالات الطوارئ')),
                ('hire_date', models.DateField(verbose_name='تاريخ التوظيف')),
                ('position', models.CharField(max_length=100, verbose_name='المنصب')),
                ('salary', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الراتب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'موظف',
                'verbose_name_plural': 'الموظفون',
            },
        ),
        migrations.CreateModel(
            name='EmployeeDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('id_copy', 'صورة الهوية'), ('certificate', 'شهادة'), ('contract', 'عقد عمل'), ('medical', 'تقرير طبي'), ('other', 'أخرى')], max_length=20, verbose_name='نوع المستند')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان المستند')),
                ('file', models.FileField(upload_to='employee_documents/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf'])], verbose_name='الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='employees.employee', verbose_name='الموظف')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='رفع بواسطة')),
            ],
            options={
                'verbose_name': 'مستند موظف',
                'verbose_name_plural': 'مستندات الموظفين',
            },
        ),
        migrations.CreateModel(
            name='Leave',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('leave_type', models.CharField(choices=[('annual', 'إجازة سنوية'), ('sick', 'إجازة مرضية'), ('emergency', 'إجازة طارئة'), ('maternity', 'إجازة أمومة'), ('study', 'إجازة دراسية')], max_length=20, verbose_name='نوع الإجازة')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('reason', models.TextField(verbose_name='السبب')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('approved', 'موافق عليها'), ('rejected', 'مرفوضة')], default='pending', max_length=10, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_leaves', to=settings.AUTH_USER_MODEL, verbose_name='موافق بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leaves', to='employees.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'إجازة',
                'verbose_name_plural': 'الإجازات',
            },
        ),
        migrations.CreateModel(
            name='Promotion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('old_position', models.CharField(max_length=100, verbose_name='المنصب السابق')),
                ('new_position', models.CharField(max_length=100, verbose_name='المنصب الجديد')),
                ('old_salary', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الراتب السابق')),
                ('new_salary', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الراتب الجديد')),
                ('promotion_date', models.DateField(verbose_name='تاريخ الترقية')),
                ('reason', models.TextField(verbose_name='سبب الترقية')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('approved_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='موافق بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='promotions', to='employees.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'ترقية',
                'verbose_name_plural': 'الترقيات',
            },
        ),
        migrations.CreateModel(
            name='Attendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('check_in', models.TimeField(blank=True, null=True, verbose_name='وقت الحضور')),
                ('check_out', models.TimeField(blank=True, null=True, verbose_name='وقت الانصراف')),
                ('is_present', models.BooleanField(default=True, verbose_name='حاضر')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendances', to='employees.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'حضور',
                'verbose_name_plural': 'الحضور',
                'unique_together': {('employee', 'date')},
            },
        ),
    ]
