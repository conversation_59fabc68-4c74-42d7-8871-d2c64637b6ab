# Generated by Django 5.2.1 on 2025-05-24 14:33

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القسم')),
                ('name_en', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم القسم بالإنجليزية')),
                ('department_type', models.CharField(choices=[('hr', 'قسم الموارد البشرية'), ('accounts', 'قسم الحسابات'), ('registration', 'قسم التسجيل'), ('academic', 'قسم أكاديمي')], max_length=20, verbose_name='نوع القسم')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='رمز القسم')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف القسم')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('head_of_department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='headed_departments', to=settings.AUTH_USER_MODEL, verbose_name='رئيس القسم')),
            ],
            options={
                'verbose_name': 'قسم',
                'verbose_name_plural': 'الأقسام',
                'ordering': ['name'],
            },
        ),
    ]
