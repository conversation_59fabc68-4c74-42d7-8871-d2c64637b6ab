#!/usr/bin/env python
"""
اختبار النظام للتأكد من عمله بشكل صحيح
"""
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')
django.setup()

def test_system():
    print("🔍 اختبار نظام إدارة الموارد البشرية")
    print("=" * 50)
    
    try:
        # اختبار استيراد النماذج
        print("✅ اختبار استيراد النماذج...")
        from accounts.models import User
        from departments.models import Department
        from employees.models import Employee
        from faculty.models import FacultyMember
        from documents.models import Document
        from notifications.models import Notification
        print("   ✓ تم استيراد جميع النماذج بنجاح")
        
        # اختبار قاعدة البيانات
        print("\n✅ اختبار قاعدة البيانات...")
        user_count = User.objects.count()
        dept_count = Department.objects.count()
        print(f"   ✓ عدد المستخدمين: {user_count}")
        print(f"   ✓ عدد الأقسام: {dept_count}")
        
        # اختبار المستخدم الإداري
        print("\n✅ اختبار المستخدم الإداري...")
        try:
            admin_user = User.objects.get(username='admin')
            print(f"   ✓ المستخدم الإداري موجود: {admin_user.get_full_name()}")
            print(f"   ✓ نوع المستخدم: {admin_user.get_user_type_display()}")
            print(f"   ✓ القسم: {admin_user.department.name if admin_user.department else 'غير محدد'}")
        except User.DoesNotExist:
            print("   ❌ المستخدم الإداري غير موجود")
            return False
        
        # اختبار الأقسام
        print("\n✅ اختبار الأقسام الإدارية...")
        departments = Department.objects.all()
        for dept in departments:
            print(f"   ✓ {dept.name} ({dept.code})")
        
        # اختبار الإعدادات
        print("\n✅ اختبار إعدادات Django...")
        from django.conf import settings
        print(f"   ✓ نموذج المستخدم: {settings.AUTH_USER_MODEL}")
        print(f"   ✓ اللغة: {settings.LANGUAGE_CODE}")
        print(f"   ✓ المنطقة الزمنية: {settings.TIME_ZONE}")
        
        # اختبار URLs
        print("\n✅ اختبار URLs...")
        from django.urls import reverse
        try:
            dashboard_url = reverse('dashboard')
            print(f"   ✓ رابط لوحة التحكم: {dashboard_url}")
        except:
            print("   ⚠️  تحذير: بعض الروابط قد لا تعمل")
        
        print("\n" + "=" * 50)
        print("🎉 النظام جاهز للاستخدام!")
        print("\n📋 معلومات الوصول:")
        print("   🌐 الرابط: http://127.0.0.1:8000")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: admin123")
        print("\n📋 لوحة الإدارة:")
        print("   🌐 الرابط: http://127.0.0.1:8000/admin")
        print("   👤 نفس بيانات تسجيل الدخول")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في النظام: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_system()
    if success:
        print("\n🚀 لتشغيل الخادم:")
        print("   python manage.py runserver")
        print("   أو انقر نقراً مزدوجاً على run.bat")
    else:
        print("\n🔧 يرجى إصلاح الأخطاء قبل تشغيل النظام")
