from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import FileExtensionValidator


class DocumentCategory(models.Model):
    """نموذج تصنيفات المستندات"""

    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم التصنيف')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('وصف التصنيف')
    )

    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='subcategories',
        verbose_name=_('التصنيف الأب')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('تصنيف مستند')
        verbose_name_plural = _('تصنيفات المستندات')
        ordering = ['name']

    def __str__(self):
        if self.parent:
            return f"{self.parent.name} - {self.name}"
        return self.name


class Document(models.Model):
    """نموذج أرشفة المستندات والوثائق"""

    PRIORITY_CHOICES = (
        ('low', _('منخفضة')),
        ('medium', _('متوسطة')),
        ('high', _('عالية')),
        ('urgent', _('عاجلة')),
    )

    STATUS_CHOICES = (
        ('draft', _('مسودة')),
        ('active', _('نشط')),
        ('archived', _('مؤرشف')),
        ('deleted', _('محذوف')),
    )

    # معلومات أساسية
    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان المستند')
    )

    document_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('رقم المستند')
    )

    document_date = models.DateField(
        verbose_name=_('تاريخ المستند')
    )

    category = models.ForeignKey(
        DocumentCategory,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('التصنيف')
    )

    # الجهات
    source = models.CharField(
        max_length=200,
        verbose_name=_('الجهة المرسلة')
    )

    destination = models.CharField(
        max_length=200,
        verbose_name=_('الجهة المستقبلة')
    )

    # محتوى المستند
    description = models.TextField(
        verbose_name=_('وصف المستند')
    )

    keywords = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        verbose_name=_('الكلمات المفتاحية'),
        help_text=_('افصل بين الكلمات بفاصلة')
    )

    # الملف
    file = models.FileField(
        upload_to='archived_documents/',
        validators=[FileExtensionValidator(allowed_extensions=['pdf', 'doc', 'docx', 'jpg', 'png'])],
        verbose_name=_('الملف')
    )

    file_size = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_('حجم الملف (بايت)')
    )

    # معلومات إضافية
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='medium',
        verbose_name=_('الأولوية')
    )

    status = models.CharField(
        max_length=10,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name=_('الحالة')
    )

    is_confidential = models.BooleanField(
        default=False,
        verbose_name=_('سري')
    )

    expiry_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ انتهاء الصلاحية')
    )

    # معلومات النظام
    uploaded_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        related_name='uploaded_documents',
        verbose_name=_('رفع بواسطة')
    )

    department = models.ForeignKey(
        'departments.Department',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('القسم المسؤول')
    )

    tags = models.ManyToManyField(
        'DocumentTag',
        blank=True,
        verbose_name=_('العلامات')
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('مستند')
        verbose_name_plural = _('المستندات')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.document_number} - {self.title}"

    def save(self, *args, **kwargs):
        if self.file:
            self.file_size = self.file.size
        super().save(*args, **kwargs)


class DocumentTag(models.Model):
    """نموذج علامات المستندات"""

    name = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('اسم العلامة')
    )

    color = models.CharField(
        max_length=7,
        default='#007bff',
        verbose_name=_('لون العلامة'),
        help_text=_('كود اللون بصيغة hex')
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _('علامة مستند')
        verbose_name_plural = _('علامات المستندات')
        ordering = ['name']

    def __str__(self):
        return self.name
