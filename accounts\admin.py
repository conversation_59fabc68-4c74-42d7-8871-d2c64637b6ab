from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _
from .models import User


@admin.register(User)
class CustomUserAdmin(UserAdmin):
    """إدارة المستخدمين المخصصة"""

    list_display = ('username', 'email', 'first_name', 'last_name', 'user_type', 'department', 'is_active_employee', 'is_staff')
    list_filter = ('user_type', 'department', 'is_active_employee', 'is_staff', 'is_superuser', 'is_active')
    search_fields = ('username', 'first_name', 'last_name', 'email', 'employee_id')
    ordering = ('username',)

    fieldsets = UserAdmin.fieldsets + (
        (_('معلومات إضافية'), {
            'fields': ('user_type', 'phone', 'employee_id', 'department', 'is_active_employee')
        }),
    )

    add_fieldsets = UserAdmin.add_fieldsets + (
        (_('معلومات إضافية'), {
            'fields': ('user_type', 'phone', 'employee_id', 'department', 'is_active_employee')
        }),
    )
