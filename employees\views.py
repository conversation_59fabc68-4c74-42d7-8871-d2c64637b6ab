from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils.translation import gettext_lazy as _

from .models import Employee, Leave, Attendance, EmployeeDocument, Promotion
from .forms import Employee<PERSON><PERSON>, LeaveForm, AttendanceForm, EmployeeDocumentForm


@login_required
def employee_list(request):
    """قائمة الموظفين"""

    # التحقق من الصلاحيات
    if request.user.user_type not in ['admin', 'hr']:
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة'))
        return redirect('dashboard')

    employees = Employee.objects.filter(is_active=True).select_related('user', 'user__department')

    # البحث
    search_query = request.GET.get('search')
    if search_query:
        employees = employees.filter(
            Q(user__first_name__icontains=search_query) |
            Q(user__last_name__icontains=search_query) |
            Q(user__employee_id__icontains=search_query) |
            Q(position__icontains=search_query)
        )

    # التصفية حسب القسم
    department_filter = request.GET.get('department')
    if department_filter:
        employees = employees.filter(user__department_id=department_filter)

    # الترقيم
    paginator = Paginator(employees, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'department_filter': department_filter,
    }

    return render(request, 'employees/employee_list.html', context)


@login_required
def employee_detail(request, employee_id):
    """تفاصيل الموظف"""

    employee = get_object_or_404(Employee, id=employee_id)

    # التحقق من الصلاحيات
    if request.user.user_type not in ['admin', 'hr'] and request.user != employee.user:
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة'))
        return redirect('dashboard')

    # الحصول على البيانات المرتبطة
    recent_leaves = employee.leaves.order_by('-created_at')[:5]
    recent_attendance = employee.attendances.order_by('-date')[:10]
    documents = employee.documents.order_by('-uploaded_at')
    promotions = employee.promotions.order_by('-promotion_date')

    context = {
        'employee': employee,
        'recent_leaves': recent_leaves,
        'recent_attendance': recent_attendance,
        'documents': documents,
        'promotions': promotions,
    }

    return render(request, 'employees/employee_detail.html', context)


@login_required
def leave_request(request):
    """طلب إجازة"""

    # التحقق من وجود ملف موظف للمستخدم
    try:
        employee = request.user.employee
    except Employee.DoesNotExist:
        messages.error(request, _('يجب أن تكون موظفاً لتتمكن من طلب إجازة'))
        return redirect('dashboard')

    if request.method == 'POST':
        form = LeaveForm(request.POST)
        if form.is_valid():
            leave = form.save(commit=False)
            leave.employee = employee
            leave.save()

            messages.success(request, _('تم تقديم طلب الإجازة بنجاح'))
            return redirect('employees:employee_detail', employee_id=employee.id)
    else:
        form = LeaveForm()

    context = {
        'form': form,
        'employee': employee,
    }

    return render(request, 'employees/leave_request.html', context)
