{% extends 'base.html' %}

{% block title %}قائمة الموظفين - نظام إدارة الموارد البشرية{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-1">
                    <i class="fas fa-users me-2"></i>
                    إدارة الموظفين
                </h1>
                <p class="text-muted">إدارة وعرض بيانات الموظفين</p>
            </div>
            <div>
                <a href="#" class="btn btn-primary">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة موظف جديد
                </a>
            </div>
        </div>
    </div>
</div>

<!-- أدوات البحث والتصفية -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search_query|default:'' }}" 
                               placeholder="البحث بالاسم أو رقم الموظف أو المنصب">
                    </div>
                    
                    <div class="col-md-3">
                        <label for="department" class="form-label">القسم</label>
                        <select class="form-select" id="department" name="department">
                            <option value="">جميع الأقسام</option>
                            <!-- سيتم إضافة الأقسام هنا -->
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <a href="{% url 'employees:employee_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>
                                إلغاء التصفية
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                <h4>{{ page_obj.paginator.count }}</h4>
                <small class="text-muted">إجمالي الموظفين</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                <h4>0</h4>
                <small class="text-muted">حاضر اليوم</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-calendar-times fa-2x text-warning mb-2"></i>
                <h4>0</h4>
                <small class="text-muted">في إجازة</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-user-plus fa-2x text-info mb-2"></i>
                <h4>0</h4>
                <small class="text-muted">موظفون جدد هذا الشهر</small>
            </div>
        </div>
    </div>
</div>

<!-- جدول الموظفين -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الموظفين
                </h5>
            </div>
            <div class="card-body">
                {% if page_obj %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الموظف</th>
                                    <th>الاسم</th>
                                    <th>المنصب</th>
                                    <th>القسم</th>
                                    <th>تاريخ التوظيف</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in page_obj %}
                                    <tr>
                                        <td>
                                            <strong>{{ employee.user.employee_id }}</strong>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ employee.user.get_full_name }}</div>
                                                    <small class="text-muted">{{ employee.user.email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ employee.position }}</td>
                                        <td>
                                            {% if employee.user.department %}
                                                <span class="badge bg-secondary">{{ employee.user.department.name }}</span>
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ employee.hire_date|date:"Y/m/d" }}</td>
                                        <td>
                                            {% if employee.is_active %}
                                                <span class="badge bg-success">نشط</span>
                                            {% else %}
                                                <span class="badge bg-danger">غير نشط</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'employees:employee_detail' employee.id %}" 
                                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="#" class="btn btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- الترقيم -->
                    {% if page_obj.has_other_pages %}
                        <nav aria-label="ترقيم الصفحات">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}">الأولى</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}">السابقة</a>
                                    </li>
                                {% endif %}
                                
                                <li class="page-item active">
                                    <span class="page-link">
                                        صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>
                                
                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}">التالية</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}">الأخيرة</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد بيانات موظفين</h5>
                        <p class="text-muted">لم يتم العثور على أي موظفين مطابقين لمعايير البحث</p>
                        <a href="#" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة موظف جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar-sm {
        width: 40px;
        height: 40px;
        font-size: 0.875rem;
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }
    
    .btn-group-sm > .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
</style>
{% endblock %}
