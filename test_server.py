#!/usr/bin/env python
"""
اختبار بسيط لخادم Django
"""
import os
import sys

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')

try:
    import django
    django.setup()
    
    from django.core.management import execute_from_command_line
    
    print("بدء تشغيل خادم Django...")
    print("الرابط: http://127.0.0.1:8000")
    print("للتوقف: اضغط Ctrl+C")
    print("-" * 50)
    
    execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:8000'])
    
except Exception as e:
    print(f"خطأ في تشغيل الخادم: {e}")
    import traceback
    traceback.print_exc()
