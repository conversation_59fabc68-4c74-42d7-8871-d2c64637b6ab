from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.db.models import Count, Q
from datetime import datetime, timedelta
import openpyxl
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

from employees.models import Employee, Leave, Attendance
from faculty.models import FacultyMember
from departments.models import Department
from documents.models import Document


@login_required
def reports_dashboard(request):
    """لوحة تحكم التقارير"""

    # التحقق من الصلاحيات
    if request.user.user_type not in ['admin', 'hr']:
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة'))
        from django.shortcuts import redirect
        return redirect('dashboard')

    # إحصائيات عامة
    context = {
        'total_employees': Employee.objects.filter(is_active=True).count(),
        'total_faculty': FacultyMember.objects.filter(is_active=True).count(),
        'total_departments': Department.objects.filter(is_active=True).count(),
        'total_documents': Document.objects.filter(status='active').count(),
        'pending_leaves': Leave.objects.filter(status='pending').count(),
        'approved_leaves': Leave.objects.filter(status='approved').count(),
    }

    return render(request, 'reports/dashboard.html', context)


@login_required
def employee_report(request):
    """تقرير الموظفين"""

    if request.user.user_type not in ['admin', 'hr']:
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة'))
        from django.shortcuts import redirect
        return redirect('dashboard')

    # الحصول على المعاملات
    report_format = request.GET.get('format', 'html')
    department_id = request.GET.get('department')

    # بناء الاستعلام
    employees = Employee.objects.filter(is_active=True).select_related('user', 'user__department')

    if department_id:
        employees = employees.filter(user__department_id=department_id)

    if report_format == 'pdf':
        return generate_employee_pdf_report(employees)
    elif report_format == 'excel':
        return generate_employee_excel_report(employees)

    # عرض HTML
    context = {
        'employees': employees,
        'departments': Department.objects.filter(is_active=True),
        'selected_department': department_id,
    }

    return render(request, 'reports/employee_report.html', context)


def generate_employee_pdf_report(employees):
    """إنتاج تقرير الموظفين بصيغة PDF"""

    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="employee_report_{datetime.now().strftime("%Y%m%d")}.pdf"'

    # إنشاء المستند
    doc = SimpleDocTemplate(response, pagesize=A4)
    elements = []

    # الأنماط
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1,  # وسط
    )

    # العنوان
    title = Paragraph("تقرير الموظفين - الكلية التربوية المفتوحة", title_style)
    elements.append(title)
    elements.append(Spacer(1, 20))

    # بيانات الجدول
    data = [['رقم الموظف', 'الاسم', 'المنصب', 'القسم', 'تاريخ التوظيف']]

    for employee in employees:
        data.append([
            employee.user.employee_id or '',
            employee.user.get_full_name(),
            employee.position,
            employee.user.department.name if employee.user.department else '',
            employee.hire_date.strftime('%Y/%m/%d')
        ])

    # إنشاء الجدول
    table = Table(data)
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    elements.append(table)

    # بناء المستند
    doc.build(elements)

    return response


def generate_employee_excel_report(employees):
    """إنتاج تقرير الموظفين بصيغة Excel"""

    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="employee_report_{datetime.now().strftime("%Y%m%d")}.xlsx"'

    # إنشاء ملف Excel
    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = 'تقرير الموظفين'

    # العناوين
    headers = ['رقم الموظف', 'الاسم الأول', 'اسم العائلة', 'المنصب', 'القسم', 'تاريخ التوظيف', 'الراتب']
    for col_num, header in enumerate(headers, 1):
        cell = worksheet.cell(row=1, column=col_num)
        cell.value = header
        cell.font = openpyxl.styles.Font(bold=True)
        cell.fill = openpyxl.styles.PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # البيانات
    for row_num, employee in enumerate(employees, 2):
        worksheet.cell(row=row_num, column=1, value=employee.user.employee_id or '')
        worksheet.cell(row=row_num, column=2, value=employee.user.first_name)
        worksheet.cell(row=row_num, column=3, value=employee.user.last_name)
        worksheet.cell(row=row_num, column=4, value=employee.position)
        worksheet.cell(row=row_num, column=5, value=employee.user.department.name if employee.user.department else '')
        worksheet.cell(row=row_num, column=6, value=employee.hire_date)
        worksheet.cell(row=row_num, column=7, value=float(employee.salary))

    # تنسيق الأعمدة
    for column in worksheet.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = (max_length + 2)
        worksheet.column_dimensions[column_letter].width = adjusted_width

    workbook.save(response)
    return response
