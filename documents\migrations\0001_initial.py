# Generated by Django 5.2.1 on 2025-05-24 14:33

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('departments', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='اسم العلامة')),
                ('color', models.CharField(default='#007bff', help_text='كود اللون بصيغة hex', max_length=7, verbose_name='لون العلامة')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'علامة مستند',
                'verbose_name_plural': 'علامات المستندات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='DocumentCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم التصنيف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف التصنيف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='documents.documentcategory', verbose_name='التصنيف الأب')),
            ],
            options={
                'verbose_name': 'تصنيف مستند',
                'verbose_name_plural': 'تصنيفات المستندات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان المستند')),
                ('document_number', models.CharField(max_length=50, unique=True, verbose_name='رقم المستند')),
                ('document_date', models.DateField(verbose_name='تاريخ المستند')),
                ('source', models.CharField(max_length=200, verbose_name='الجهة المرسلة')),
                ('destination', models.CharField(max_length=200, verbose_name='الجهة المستقبلة')),
                ('description', models.TextField(verbose_name='وصف المستند')),
                ('keywords', models.CharField(blank=True, help_text='افصل بين الكلمات بفاصلة', max_length=500, null=True, verbose_name='الكلمات المفتاحية')),
                ('file', models.FileField(upload_to='archived_documents/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'doc', 'docx', 'jpg', 'png'])], verbose_name='الملف')),
                ('file_size', models.PositiveIntegerField(blank=True, null=True, verbose_name='حجم الملف (بايت)')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('active', 'نشط'), ('archived', 'مؤرشف'), ('deleted', 'محذوف')], default='active', max_length=10, verbose_name='الحالة')),
                ('is_confidential', models.BooleanField(default=False, verbose_name='سري')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء الصلاحية')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('department', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='departments.department', verbose_name='القسم المسؤول')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_documents', to=settings.AUTH_USER_MODEL, verbose_name='رفع بواسطة')),
                ('category', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='documents.documentcategory', verbose_name='التصنيف')),
                ('tags', models.ManyToManyField(blank=True, to='documents.documenttag', verbose_name='العلامات')),
            ],
            options={
                'verbose_name': 'مستند',
                'verbose_name_plural': 'المستندات',
                'ordering': ['-created_at'],
            },
        ),
    ]
