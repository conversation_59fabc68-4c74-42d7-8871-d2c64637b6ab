from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _


class User(AbstractUser):
    """نموذج المستخدم المخصص"""

    USER_TYPES = (
        ('admin', _('مدير النظام')),
        ('hr', _('موظف الموارد البشرية')),
        ('student_affairs', _('موظف شؤون الطلاب')),
        ('lecturer', _('محاضر')),
        ('employee', _('موظف')),
    )

    user_type = models.CharField(
        max_length=20,
        choices=USER_TYPES,
        default='employee',
        verbose_name=_('نوع المستخدم')
    )

    phone = models.CharField(
        max_length=15,
        blank=True,
        null=True,
        verbose_name=_('رقم الهاتف')
    )

    employee_id = models.CharField(
        max_length=20,
        unique=True,
        blank=True,
        null=True,
        verbose_name=_('رقم الموظف')
    )

    department = models.ForeignKey(
        'departments.Department',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('القسم')
    )

    is_active_employee = models.BooleanField(
        default=True,
        verbose_name=_('موظف نشط')
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('مستخدم')
        verbose_name_plural = _('المستخدمون')

    def __str__(self):
        return f"{self.get_full_name()} ({self.employee_id})"

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}".strip() or self.username
