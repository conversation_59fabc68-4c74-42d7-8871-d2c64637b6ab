from django.db import models
from django.utils.translation import gettext_lazy as _


class Department(models.Model):
    """نموذج الأقسام الإدارية"""

    DEPARTMENT_TYPES = (
        ('hr', _('قسم الموارد البشرية')),
        ('accounts', _('قسم الحسابات')),
        ('registration', _('قسم التسجيل')),
        ('academic', _('قسم أكاديمي')),
    )

    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم القسم')
    )

    name_en = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('اسم القسم بالإنجليزية')
    )

    department_type = models.CharField(
        max_length=20,
        choices=DEPARTMENT_TYPES,
        verbose_name=_('نوع القسم')
    )

    code = models.CharField(
        max_length=10,
        unique=True,
        verbose_name=_('رمز القسم')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('وصف القسم')
    )

    head_of_department = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='headed_departments',
        verbose_name=_('رئيس القسم')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('قسم')
        verbose_name_plural = _('الأقسام')
        ordering = ['name']

    def __str__(self):
        return self.name
