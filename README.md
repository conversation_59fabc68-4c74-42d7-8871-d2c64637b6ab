# نظام إدارة الموارد البشرية - الكلية التربوية المفتوحة

نظام شامل لإدارة الموارد البشرية مطور باستخدام Django، مصمم خصيصاً للكلية التربوية المفتوحة.

## المميزات الرئيسية

### 🔐 نظام المصادقة والصلاحيات
- تسجيل دخول آمن مع أنواع مستخدمين متعددة
- صلاحيات متدرجة (مدير النظام، موظف الموارد البشرية، موظف شؤون الطلاب، محاضر، موظف)
- واجهة تسجيل دخول عربية متجاوبة

### 👥 إدارة الموظفين
- إضافة وتعديل بيانات الموظفين الشخصية والوظيفية
- تتبع الحضور والانصراف
- إدارة طلبات الإجازات (سنوية، مرضية، طارئة، أمومة، دراسية)
- تتبع الترقيات وتاريخ التوظيف
- رفع وإدارة مستمسكات الموظفين (PDF)

### 🎓 إدارة أعضاء هيئة التدريس
- إدارة البيانات الأكاديمية والشخصية
- تتبع الرتب الأكاديمية والتخصصات
- إدارة الجداول الدراسية والمقررات
- رفع وإدارة المستندات الأكاديمية

### 🏢 الأقسام الإدارية
- قسم الموارد البشرية
- قسم الحسابات
- قسم التسجيل
- إدارة رؤساء الأقسام

### 📁 نظام أرشفة المستندات
- تصنيف المستندات حسب النوع والأولوية
- البحث المتقدم بالكلمات المفتاحية
- تتبع مصدر ووجهة المستندات
- نظام العلامات والتصنيفات
- دعم ملفات PDF, DOC, DOCX, JPG, PNG

### 🔔 نظام التنبيهات
- تنبيهات إدارية وأكاديمية
- تنبيهات الإجازات وانتهاء صلاحية المستندات
- قوالب تنبيهات قابلة للتخصيص
- إرسال تنبيهات جماعية

### 📊 نظام التقارير
- تقارير شهرية وسنوية
- تصدير التقارير بصيغة PDF و Excel
- تقارير الموظفين والحضور
- تقارير الإجازات والترقيات

## التقنيات المستخدمة

- **Backend**: Django 5.2.1
- **Database**: SQLite (قابل للترقية إلى PostgreSQL)
- **Frontend**: Bootstrap 5 (RTL) + Font Awesome
- **التقارير**: ReportLab (PDF) + OpenPyXL (Excel)
- **معالجة الصور**: Pillow
- **الخطوط العربية**: Cairo Font

## متطلبات النظام

```
Python 3.8+
Django 5.2.1
Pillow 11.2.1
reportlab 4.4.1
openpyxl 3.1.5
python-decouple 3.8
```

## التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone [repository-url]
cd HR1
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. إعداد قاعدة البيانات
```bash
python manage.py makemigrations
python manage.py migrate
```

### 4. إنشاء مستخدم إداري
```bash
python manage.py createsuperuser
```

### 5. تشغيل الخادم
```bash
python manage.py runserver
```

### 6. الوصول للنظام
- الواجهة الرئيسية: http://127.0.0.1:8000
- لوحة الإدارة: http://127.0.0.1:8000/admin

## بيانات تجريبية

### المستخدم الإداري الافتراضي:
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- **البريد الإلكتروني**: <EMAIL>

### الأقسام الإدارية المُنشأة:
1. قسم الموارد البشرية (HR001)
2. قسم الحسابات (ACC001)
3. قسم التسجيل (REG001)

## هيكل المشروع

```
HR1/
├── accounts/           # إدارة المستخدمين والمصادقة
├── employees/          # إدارة الموظفين
├── faculty/           # إدارة أعضاء هيئة التدريس
├── departments/       # إدارة الأقسام
├── documents/         # نظام أرشفة المستندات
├── notifications/     # نظام التنبيهات
├── reports/          # نظام التقارير
├── templates/        # قوالب HTML
├── static/          # ملفات CSS/JS/Images
├── media/           # ملفات المستخدمين المرفوعة
└── hr_system/       # إعدادات المشروع الرئيسية
```

## أنواع المستخدمين والصلاحيات

### 1. مدير النظام (admin)
- صلاحيات كاملة على جميع أجزاء النظام
- إدارة المستخدمين والأقسام
- عرض جميع التقارير

### 2. موظف الموارد البشرية (hr)
- إدارة بيانات الموظفين
- الموافقة على طلبات الإجازات
- إنتاج التقارير

### 3. موظف شؤون الطلاب (student_affairs)
- إدارة بيانات أعضاء هيئة التدريس
- إدارة الجداول الدراسية

### 4. محاضر (lecturer)
- عرض البيانات الشخصية
- عرض الجدول الدراسي
- طلب الإجازات

### 5. موظف (employee)
- عرض البيانات الشخصية
- طلب الإجازات
- عرض كشف الحضور

## المميزات التقنية

- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **دعم اللغة العربية**: واجهة كاملة باللغة العربية مع دعم RTL
- **أمان عالي**: حماية CSRF وتشفير كلمات المرور
- **قابلية التوسع**: هيكل معياري قابل للتطوير
- **تحسين الأداء**: استعلامات محسنة وفهرسة قاعدة البيانات

## الدعم والصيانة

للحصول على الدعم التقني أو الإبلاغ عن مشاكل:
- إنشاء Issue في المستودع
- التواصل مع فريق تطوير النظام

## الترخيص

هذا المشروع مطور خصيصاً للكلية التربوية المفتوحة.

## المطورون

تم تطوير هذا النظام بواسطة فريق تطوير متخصص في أنظمة إدارة الموارد البشرية.

---

**ملاحظة**: هذا النظام في مرحلة التطوير المستمر، وسيتم إضافة المزيد من المميزات تدريجياً.
