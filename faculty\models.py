from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import FileExtensionValidator


class FacultyMember(models.Model):
    """نموذج أعضاء هيئة التدريس"""

    ACADEMIC_RANKS = (
        ('assistant_lecturer', _('مدرس مساعد')),
        ('lecturer', _('مدرس')),
        ('assistant_professor', _('أستاذ مساعد')),
        ('associate_professor', _('أستاذ مشارك')),
        ('professor', _('أستاذ')),
    )

    EMPLOYMENT_TYPES = (
        ('full_time', _('دوام كامل')),
        ('part_time', _('دوام جزئي')),
        ('visiting', _('زائر')),
        ('adjunct', _('منتدب')),
    )

    user = models.OneToOneField(
        'accounts.User',
        on_delete=models.CASCADE,
        verbose_name=_('المستخدم')
    )

    # المعلومات الأكاديمية
    academic_rank = models.CharField(
        max_length=30,
        choices=ACADEMIC_RANKS,
        verbose_name=_('الرتبة الأكاديمية')
    )

    specialization = models.CharField(
        max_length=200,
        verbose_name=_('التخصص')
    )

    highest_degree = models.CharField(
        max_length=100,
        verbose_name=_('أعلى شهادة')
    )

    university_graduated = models.CharField(
        max_length=200,
        verbose_name=_('الجامعة المتخرج منها')
    )

    graduation_year = models.IntegerField(
        verbose_name=_('سنة التخرج')
    )

    # معلومات التوظيف
    employment_type = models.CharField(
        max_length=20,
        choices=EMPLOYMENT_TYPES,
        default='full_time',
        verbose_name=_('نوع التوظيف')
    )

    hire_date = models.DateField(
        verbose_name=_('تاريخ التوظيف')
    )

    office_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('رقم المكتب')
    )

    office_hours = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ساعات المكتب')
    )

    research_interests = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('الاهتمامات البحثية')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('عضو هيئة تدريس')
        verbose_name_plural = _('أعضاء هيئة التدريس')

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.get_academic_rank_display()}"


class Course(models.Model):
    """نموذج المقررات الدراسية"""

    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('رمز المقرر')
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('اسم المقرر')
    )

    name_en = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('اسم المقرر بالإنجليزية')
    )

    credit_hours = models.IntegerField(
        verbose_name=_('الساعات المعتمدة')
    )

    department = models.ForeignKey(
        'departments.Department',
        on_delete=models.CASCADE,
        verbose_name=_('القسم')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('وصف المقرر')
    )

    prerequisites = models.ManyToManyField(
        'self',
        blank=True,
        symmetrical=False,
        verbose_name=_('المتطلبات السابقة')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('مقرر')
        verbose_name_plural = _('المقررات')

    def __str__(self):
        return f"{self.code} - {self.name}"


class Schedule(models.Model):
    """نموذج الجداول الدراسية"""

    DAYS_OF_WEEK = (
        ('sunday', _('الأحد')),
        ('monday', _('الاثنين')),
        ('tuesday', _('الثلاثاء')),
        ('wednesday', _('الأربعاء')),
        ('thursday', _('الخميس')),
        ('friday', _('الجمعة')),
        ('saturday', _('السبت')),
    )

    SEMESTERS = (
        ('fall', _('الفصل الأول')),
        ('spring', _('الفصل الثاني')),
        ('summer', _('الفصل الصيفي')),
    )

    faculty_member = models.ForeignKey(
        FacultyMember,
        on_delete=models.CASCADE,
        related_name='schedules',
        verbose_name=_('عضو هيئة التدريس')
    )

    course = models.ForeignKey(
        Course,
        on_delete=models.CASCADE,
        verbose_name=_('المقرر')
    )

    semester = models.CharField(
        max_length=10,
        choices=SEMESTERS,
        verbose_name=_('الفصل الدراسي')
    )

    academic_year = models.CharField(
        max_length=9,
        verbose_name=_('السنة الأكاديمية'),
        help_text=_('مثال: 2023-2024')
    )

    day_of_week = models.CharField(
        max_length=10,
        choices=DAYS_OF_WEEK,
        verbose_name=_('يوم الأسبوع')
    )

    start_time = models.TimeField(
        verbose_name=_('وقت البداية')
    )

    end_time = models.TimeField(
        verbose_name=_('وقت النهاية')
    )

    classroom = models.CharField(
        max_length=50,
        verbose_name=_('القاعة الدراسية')
    )

    class Meta:
        verbose_name = _('جدول دراسي')
        verbose_name_plural = _('الجداول الدراسية')
        unique_together = ['faculty_member', 'course', 'semester', 'academic_year', 'day_of_week', 'start_time']

    def __str__(self):
        return f"{self.faculty_member.user.get_full_name()} - {self.course.name} - {self.get_day_of_week_display()}"


class FacultyDocument(models.Model):
    """نموذج مستمسكات أعضاء هيئة التدريس"""

    DOCUMENT_TYPES = (
        ('cv', _('السيرة الذاتية')),
        ('degree', _('شهادة علمية')),
        ('transcript', _('كشف درجات')),
        ('research', _('بحث علمي')),
        ('certificate', _('شهادة تقدير')),
        ('contract', _('عقد عمل')),
        ('other', _('أخرى')),
    )

    faculty_member = models.ForeignKey(
        FacultyMember,
        on_delete=models.CASCADE,
        related_name='documents',
        verbose_name=_('عضو هيئة التدريس')
    )

    document_type = models.CharField(
        max_length=20,
        choices=DOCUMENT_TYPES,
        verbose_name=_('نوع المستند')
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان المستند')
    )

    file = models.FileField(
        upload_to='faculty_documents/',
        validators=[FileExtensionValidator(allowed_extensions=['pdf'])],
        verbose_name=_('الملف')
    )

    uploaded_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('رفع بواسطة')
    )

    uploaded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _('مستند عضو هيئة تدريس')
        verbose_name_plural = _('مستندات أعضاء هيئة التدريس')

    def __str__(self):
        return f"{self.faculty_member.user.get_full_name()} - {self.title}"
