from django.db import models
from django.utils.translation import gettext_lazy as _


class Notification(models.Model):
    """نموذج التنبيهات"""

    NOTIFICATION_TYPES = (
        ('admin', _('إداري')),
        ('academic', _('أكاديمي')),
        ('leave', _('إجازة')),
        ('document', _('مستند')),
        ('system', _('نظام')),
        ('reminder', _('تذكير')),
    )

    PRIORITY_LEVELS = (
        ('low', _('منخفضة')),
        ('medium', _('متوسطة')),
        ('high', _('عالية')),
        ('urgent', _('عاجلة')),
    )

    # المحتوى
    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان التنبيه')
    )

    message = models.TextField(
        verbose_name=_('نص التنبيه')
    )

    notification_type = models.CharField(
        max_length=20,
        choices=NOTIFICATION_TYPES,
        verbose_name=_('نوع التنبيه')
    )

    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_LEVELS,
        default='medium',
        verbose_name=_('الأولوية')
    )

    # المرسل والمستقبل
    sender = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='sent_notifications',
        verbose_name=_('المرسل')
    )

    recipient = models.ForeignKey(
        'accounts.User',
        on_delete=models.CASCADE,
        related_name='received_notifications',
        verbose_name=_('المستقبل')
    )

    # الحالة
    is_read = models.BooleanField(
        default=False,
        verbose_name=_('مقروء')
    )

    read_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('وقت القراءة')
    )

    # معلومات إضافية
    related_object_type = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('نوع الكائن المرتبط')
    )

    related_object_id = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name=_('معرف الكائن المرتبط')
    )

    action_url = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('رابط الإجراء')
    )

    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('ينتهي في')
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('تنبيه')
        verbose_name_plural = _('التنبيهات')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.recipient.get_full_name()}"

    def mark_as_read(self):
        """تحديد التنبيه كمقروء"""
        from django.utils import timezone
        self.is_read = True
        self.read_at = timezone.now()
        self.save()


class NotificationTemplate(models.Model):
    """نموذج قوالب التنبيهات"""

    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم القالب')
    )

    notification_type = models.CharField(
        max_length=20,
        choices=Notification.NOTIFICATION_TYPES,
        verbose_name=_('نوع التنبيه')
    )

    title_template = models.CharField(
        max_length=200,
        verbose_name=_('قالب العنوان'),
        help_text=_('يمكن استخدام متغيرات مثل {user_name}, {date}')
    )

    message_template = models.TextField(
        verbose_name=_('قالب الرسالة'),
        help_text=_('يمكن استخدام متغيرات مثل {user_name}, {date}')
    )

    default_priority = models.CharField(
        max_length=10,
        choices=Notification.PRIORITY_LEVELS,
        default='medium',
        verbose_name=_('الأولوية الافتراضية')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('قالب تنبيه')
        verbose_name_plural = _('قوالب التنبيهات')

    def __str__(self):
        return self.name


class BulkNotification(models.Model):
    """نموذج التنبيهات الجماعية"""

    TARGET_TYPES = (
        ('all', _('جميع المستخدمين')),
        ('department', _('قسم محدد')),
        ('user_type', _('نوع مستخدم محدد')),
        ('custom', _('مستخدمون محددون')),
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان التنبيه')
    )

    message = models.TextField(
        verbose_name=_('نص التنبيه')
    )

    notification_type = models.CharField(
        max_length=20,
        choices=Notification.NOTIFICATION_TYPES,
        verbose_name=_('نوع التنبيه')
    )

    priority = models.CharField(
        max_length=10,
        choices=Notification.PRIORITY_LEVELS,
        default='medium',
        verbose_name=_('الأولوية')
    )

    target_type = models.CharField(
        max_length=20,
        choices=TARGET_TYPES,
        verbose_name=_('نوع الهدف')
    )

    target_department = models.ForeignKey(
        'departments.Department',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('القسم المستهدف')
    )

    target_user_type = models.CharField(
        max_length=20,
        choices=[],  # سيتم ملؤها من User.USER_TYPES
        blank=True,
        null=True,
        verbose_name=_('نوع المستخدم المستهدف')
    )

    target_users = models.ManyToManyField(
        'accounts.User',
        blank=True,
        verbose_name=_('المستخدمون المستهدفون')
    )

    sender = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        related_name='sent_bulk_notifications',
        verbose_name=_('المرسل')
    )

    is_sent = models.BooleanField(
        default=False,
        verbose_name=_('تم الإرسال')
    )

    sent_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('وقت الإرسال')
    )

    recipients_count = models.PositiveIntegerField(
        default=0,
        verbose_name=_('عدد المستقبلين')
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _('تنبيه جماعي')
        verbose_name_plural = _('التنبيهات الجماعية')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.get_target_type_display()}"
