from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth import login, authenticate
from django.contrib import messages
from django.db.models import Count
from django.utils.translation import gettext_lazy as _

from employees.models import Employee
from faculty.models import FacultyMember
from departments.models import Department
from documents.models import Document


@login_required
def dashboard(request):
    """لوحة التحكم الرئيسية"""

    # إحصائيات عامة
    context = {
        'total_employees': Employee.objects.filter(is_active=True).count(),
        'total_faculty': FacultyMember.objects.filter(is_active=True).count(),
        'total_departments': Department.objects.filter(is_active=True).count(),
        'total_documents': Document.objects.filter(status='active').count(),
    }

    # إحصائيات حسب نوع المستخدم
    if request.user.user_type in ['admin', 'hr']:
        # إحصائيات إضافية للمديرين وموظفي الموارد البشرية
        context.update({
            'pending_leaves': 0,  # سيتم تحديثها لاحقاً
            'recent_hires': Employee.objects.filter(is_active=True).order_by('-hire_date')[:5],
            'departments_stats': Department.objects.annotate(
                employee_count=Count('user__employee')
            ).filter(is_active=True)
        })

    return render(request, 'dashboard.html', context)


@login_required
def profile(request):
    """الملف الشخصي للمستخدم"""

    context = {
        'user': request.user,
    }

    # إضافة معلومات الموظف أو عضو هيئة التدريس
    try:
        if hasattr(request.user, 'employee'):
            context['employee'] = request.user.employee
        elif hasattr(request.user, 'facultymember'):
            context['faculty_member'] = request.user.facultymember
    except:
        pass

    return render(request, 'accounts/profile.html', context)


def custom_login(request):
    """صفحة تسجيل الدخول المخصصة"""

    if request.user.is_authenticated:
        return redirect('dashboard')

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            messages.success(request, _('تم تسجيل الدخول بنجاح'))

            # توجيه المستخدم حسب نوعه
            next_url = request.GET.get('next', 'dashboard')
            return redirect(next_url)
        else:
            messages.error(request, _('اسم المستخدم أو كلمة المرور غير صحيحة'))

    return render(request, 'accounts/login.html')
